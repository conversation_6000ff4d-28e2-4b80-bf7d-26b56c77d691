#include "qrcodedetector.h"
#include <QDebug>
#include <QTime>
#include <opencv2/opencv.hpp>

/**
 * 性能测试工具 - 比较优化前后的性能差异
 */
class QRCodePerformanceTest
{
public:
    static void testOptimizedDetection(const cv::Mat& testImage)
    {
        QRCodeDetector detector;
        
        if (!detector.initialize()) {
            qWarning() << "Failed to initialize detector";
            return;
        }
        
        qDebug() << "=== QR Code Detection Performance Test ===";
        qDebug() << "Image size:" << testImage.cols << "x" << testImage.rows;
        
        // 测试优化后的方法
        QTime timer;
        timer.start();
        
        QStringList results = detector.detectQRCodesWithFinderPatterns(testImage);
        
        int optimizedTime = timer.elapsed();
        
        qDebug() << "Optimized method:";
        qDebug() << "  - Time taken:" << optimizedTime << "ms";
        qDebug() << "  - Results found:" << results.size();
        
        if (!results.isEmpty()) {
            qDebug() << "  - First result:" << results.first();
        }
        
        // 测试原始多角度检测方法（作为对比）
        timer.restart();
        
        QStringList originalResults = detector.detectQRCodesWithRotation(testImage);
        
        int originalTime = timer.elapsed();
        
        qDebug() << "Original rotation method:";
        qDebug() << "  - Time taken:" << originalTime << "ms";
        qDebug() << "  - Results found:" << originalResults.size();
        
        if (!originalResults.isEmpty()) {
            qDebug() << "  - First result:" << originalResults.first();
        }
        
        // 性能对比
        if (optimizedTime > 0 && originalTime > 0) {
            double speedup = double(originalTime) / optimizedTime;
            qDebug() << "Performance improvement:" << QString::number(speedup, 'f', 2) << "x faster";
            
            if (speedup > 1.5) {
                qDebug() << "✓ Significant performance improvement achieved!";
            } else if (speedup > 1.0) {
                qDebug() << "✓ Moderate performance improvement achieved.";
            } else {
                qDebug() << "⚠ Performance may need further optimization.";
            }
        }
        
        qDebug() << "=== Test Complete ===";
    }
    
    static void runMemoryUsageTest(const cv::Mat& testImage)
    {
        qDebug() << "=== Memory Usage Test ===";
        
        QRCodeDetector detector;
        detector.initialize();
        
        // 测试多次检测的内存稳定性
        for (int i = 0; i < 5; i++) {
            qDebug() << "Test iteration" << (i + 1);
            
            QTime timer;
            timer.start();
            
            QStringList results = detector.detectQRCodesWithFinderPatterns(testImage);
            
            int elapsed = timer.elapsed();
            qDebug() << "  - Time:" << elapsed << "ms, Results:" << results.size();
            
            // 强制垃圾回收（如果需要）
            // 在实际应用中，OpenCV的Mat对象应该自动管理内存
        }
        
        qDebug() << "=== Memory Test Complete ===";
    }
};

// 使用示例：
// QRCodePerformanceTest::testOptimizedDetection(yourTestImage);
